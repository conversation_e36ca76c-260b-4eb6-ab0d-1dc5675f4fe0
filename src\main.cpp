#include "core/Application.hpp"
#include <spdlog/spdlog.h>

std::unique_ptr<Application> app;

int main(int argc, char *argv[])
{
#ifdef _WIN32
    // Set console to UTF-8 encoding for proper character display
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
#endif

    spdlog::set_pattern("[%Y%m%d %H:%M:%S.%e][%L] %v. [%@, %t]");

    app = std::make_unique<Application>("config.json");
    app->start();

    return 0;
}
