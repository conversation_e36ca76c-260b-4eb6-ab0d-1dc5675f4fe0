#include "core/Application.hpp"
#include <spdlog/spdlog.h>

#ifdef _WIN32
    #include <windows.h>
#else
    #include <unistd.h>
#endif

std::unique_ptr<Application> app;

int main(int argc, char *argv[])
{
#ifdef _WIN32
    // Set console to UTF-8 encoding for proper character display
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
#endif

    try
    {
        app = std::make_unique<Application>("config.json");
        app->start();
    }
    catch (const std::exception &e)
    {
        spdlog::error("Application error: {}", e.what());
        return 1;
    }

    return 0;
}
