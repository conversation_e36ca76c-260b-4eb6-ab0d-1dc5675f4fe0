#pragma once

#include "models/Product.hpp"
#include "database/DatabaseManager.hpp"
#include <vector>
#include <optional>
#include <spdlog/spdlog.h>

class ProductDao {
public:
    ProductDao(DatabaseManager& db_manager) : db_manager_(db_manager) {}
    
    bool insert(const Product& product) {
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, R"(
                INSERT OR REPLACE INTO products (id, product_name, product_description, product_start_time, 
                                               product_end_time, group_name)
                VALUES (?, ?, ?, ?, ?, ?)
            )");
            
            query.bind(1, product.id);
            query.bind(2, product.product_name);
            query.bind(3, product.product_description);
            query.bind(4, product.product_start_time);
            query.bind(5, product.product_end_time);
            query.bind(6, product.group_name);
            
            return query.exec() > 0;
        } catch (const std::exception& e) {
            spdlog::error("ProductDao::insert error: {}", e.what());
            return false;
        }
    }
    
    std::vector<Product> findAll() {
        std::vector<Product> products;
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, "SELECT * FROM products ORDER BY id");
            
            while (query.executeStep()) {
                Product product;
                product.id = query.getColumn(0).getInt();
                product.product_name = query.getColumn(1).getString();
                product.product_description = query.getColumn(2).getString();
                product.product_start_time = query.getColumn(3).getString();
                product.product_end_time = query.getColumn(4).getString();
                product.group_name = query.getColumn(5).getString();
                products.push_back(product);
            }
        } catch (const std::exception& e) {
            spdlog::error("ProductDao::findAll error: {}", e.what());
        }
        return products;
    }
    
    bool insertOrUpdate(const Product& product) {
        return insert(product);
    }

    bool batchInsertOrUpdate(const std::vector<Product>& products) {
        if (products.empty()) {
            return true;
        }

        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Transaction transaction(db);

            int processed = 0;
            for (const auto& product : products) {
                if (insertOrUpdate(product)) {
                    processed++;
                }
            }

            transaction.commit();
            spdlog::info("ProductDao: Batch operation completed - {}/{} records processed successfully",
                        processed, products.size());
            return processed == products.size();

        } catch (const std::exception& e) {
            spdlog::error("ProductDao::batchInsertOrUpdate error: {}", e.what());
            return false;
        }
    }

private:
    DatabaseManager& db_manager_;
};
