#pragma once

#include <httplib.h>
#include <spdlog/spdlog.h>
#include <unordered_map>
#include <algorithm>

#ifdef EMBED_RESOURCES
#include "resources/EmbeddedResources.hpp"
#else
#include <filesystem>
#include <fstream>
#endif

class StaticController {
public:
    static void setupRoutes(httplib::Server& server) {
        // Serve index.html at root
        server.Get("/", [](const httplib::Request& req, httplib::Response& res) {
            serveFile("/index.html", res);
        });

        // Serve static files - but exclude API paths
        server.Get(R"(/(.+))", [](const httplib::Request& req, httplib::Response& res) {
            auto matches = req.matches;
            std::string path = matches[1];

            // Don't serve API paths as static files
            if (path.substr(0, 4) == "api/") {
                spdlog::warn("Static controller rejecting API path: /{}", path);
                res.status = 404;
                res.body = "API endpoint not found";
                return;
            }
            serveFile("/" + path, res);
        });
    }

private:
    static void serveFile(const std::string& path, httplib::Response& res) {
#ifdef EMBED_RESOURCES
        serveEmbeddedFile(path, res);
#else
        serveFileFromDisk(path, res);
#endif
    }

#ifdef EMBED_RESOURCES
    static void serveEmbeddedFile(const std::string& path, httplib::Response& res) {
        try {
            const auto* resource = EmbeddedResources::getResource(path);
            if (resource) {
                res.status = 200;
                res.set_header("Content-Type", resource->mime_type);
                res.set_header("Cache-Control", "public, max-age=3600");
                res.body = std::string(reinterpret_cast<const char*>(resource->data), resource->size);

                spdlog::debug("Serving embedded resource: {}", path);
                return;
            }

            spdlog::warn("Embedded resource not found: {}", path);
            res.status = 404;
            res.body = "File not found";

        } catch (const std::exception& e) {
            spdlog::error("Error serving embedded resource {}: {}", path, e.what());
            res.status = 500;
            res.body = "Internal server error";
        }
    }
#endif

    static void serveFileFromDisk(const std::string& path, httplib::Response& res) {
        try {
            std::string file_path = "web" + path;

            // Security check: prevent directory traversal
            if (path.find("..") != std::string::npos) {
                spdlog::warn("Directory traversal attempt: {}", path);
                res.status = 403;
                res.body = "Forbidden";
                return;
            }

            if (!std::filesystem::exists(file_path)) {
                spdlog::warn("File not found: {}", file_path);
                res.status = 404;
                res.body = "File not found";
                return;
            }

            std::ifstream file(file_path, std::ios::binary);
            if (!file.is_open()) {
                spdlog::error("Cannot open file: {}", file_path);
                res.status = 500;
                res.body = "Cannot open file";
                return;
            }

            std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());

            res.status = 200;
            res.set_header("Content-Type", getMimeType(path));
            res.set_header("Cache-Control", "public, max-age=3600");
            res.body = content;

            spdlog::debug("Serving file from disk: {}", file_path);

        } catch (const std::exception& e) {
            spdlog::error("Error serving file {}: {}", path, e.what());
            res.status = 500;
            res.body = "Internal server error";
        }
    }
    
    static std::string getMimeType(const std::string& path) {
        std::filesystem::path file_path(path);
        std::string ext = file_path.extension().string();
        std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
        
        static const std::unordered_map<std::string, std::string> mime_types = {
            {".html", "text/html"},
            {".css", "text/css"},
            {".js", "application/javascript"},
            {".json", "application/json"},
            {".png", "image/png"},
            {".jpg", "image/jpeg"},
            {".jpeg", "image/jpeg"},
            {".gif", "image/gif"},
            {".svg", "image/svg+xml"},
            {".ico", "image/x-icon"},
            {".txt", "text/plain"}
        };
        
        auto it = mime_types.find(ext);
        return (it != mime_types.end()) ? it->second : "application/octet-stream";
    }
};
