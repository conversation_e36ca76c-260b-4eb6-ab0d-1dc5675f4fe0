#pragma once

#include <crow.h>
#include <spdlog/spdlog.h>
#include <unordered_map>
#include <algorithm>

#ifdef EMBED_RESOURCES
#include "resources/EmbeddedResources.hpp"
#else
#include <filesystem>
#include <fstream>
#endif

class StaticController {
public:
    static void setupRoutes(crow::SimpleApp& app) {
        // Serve index.html at root
        CROW_ROUTE(app, "/")
        ([](const crow::request& req) {
            return serveFile("/index.html");
        });

        // Serve static files - but exclude API paths
        CROW_ROUTE(app, "/<path>")
        ([](const crow::request& req, const std::string& path) {
            // Don't serve API paths as static files
            if (path.substr(0, 4) == "api/") {
                spdlog::warn("Static controller rejecting API path: /{}", path);
                return crow::response(404, "API endpoint not found");
            }
            return serveFile("/" + path);
        });
    }

private:
    static crow::response serveFile(const std::string& path) {
#ifdef EMBED_RESOURCES
        return serveEmbeddedFile(path);
#else
        return serveFileFromDisk(path);
#endif
    }

#ifdef EMBED_RESOURCES
    static crow::response serveEmbeddedFile(const std::string& path) {
        try {
            const auto* resource = EmbeddedResources::getResource(path);
            if (resource) {
                crow::response res(200);
                res.set_header("Content-Type", resource->mime_type);
                res.set_header("Cache-Control", "public, max-age=3600");
                res.body = std::string(reinterpret_cast<const char*>(resource->data), resource->size);
                
                spdlog::debug("Serving embedded resource: {}", path);
                return res;
            }
            
            spdlog::warn("Embedded resource not found: {}", path);
            return crow::response(404, "File not found");
            
        } catch (const std::exception& e) {
            spdlog::error("Error serving embedded resource {}: {}", path, e.what());
            return crow::response(500, "Internal server error");
        }
    }
#endif

    static crow::response serveFileFromDisk(const std::string& path) {
        try {
            std::string file_path = "web" + path;
            
            // Security check: prevent directory traversal
            if (path.find("..") != std::string::npos) {
                spdlog::warn("Directory traversal attempt: {}", path);
                return crow::response(403, "Forbidden");
            }
            
            if (!std::filesystem::exists(file_path)) {
                spdlog::warn("File not found: {}", file_path);
                return crow::response(404, "File not found");
            }
            
            std::ifstream file(file_path, std::ios::binary);
            if (!file.is_open()) {
                spdlog::error("Cannot open file: {}", file_path);
                return crow::response(500, "Cannot open file");
            }
            
            std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
            
            crow::response res(200);
            res.set_header("Content-Type", getMimeType(path));
            res.set_header("Cache-Control", "public, max-age=3600");
            res.body = content;
            
            spdlog::debug("Serving file from disk: {}", file_path);
            return res;
            
        } catch (const std::exception& e) {
            spdlog::error("Error serving file {}: {}", path, e.what());
            return crow::response(500, "Internal server error");
        }
    }
    
    static std::string getMimeType(const std::string& path) {
        std::filesystem::path file_path(path);
        std::string ext = file_path.extension().string();
        std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
        
        static const std::unordered_map<std::string, std::string> mime_types = {
            {".html", "text/html"},
            {".css", "text/css"},
            {".js", "application/javascript"},
            {".json", "application/json"},
            {".png", "image/png"},
            {".jpg", "image/jpeg"},
            {".jpeg", "image/jpeg"},
            {".gif", "image/gif"},
            {".svg", "image/svg+xml"},
            {".ico", "image/x-icon"},
            {".txt", "text/plain"}
        };
        
        auto it = mime_types.find(ext);
        return (it != mime_types.end()) ? it->second : "application/octet-stream";
    }
};
