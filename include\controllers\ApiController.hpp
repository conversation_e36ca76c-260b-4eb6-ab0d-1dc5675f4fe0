#pragma once

#include "core/Application.hpp"
#include "dao/CustomerDao.hpp"
#include "dao/HolidayDao.hpp"
#include "dao/ProductDao.hpp"
#include "dao/ProjectDao.hpp"
#include "dao/UserDao.hpp"
#include "dao/WorkHourDao.hpp"
#include "database/DatabaseManager.hpp"
#include "services/SyncService.hpp"
#include <algorithm>
#include <chrono>
#include <crow.h>
#include <fstream>
#include <iomanip>
#include <nlohmann/json.hpp>
#include <set>
#include <sstream>
#include <thread>

class ApiController
{
public:
    static void setupRoutes(crow::SimpleApp &app)
    {
        CROW_ROUTE(app, "/api/products").methods("GET"_method)(getProducts);
        CROW_ROUTE(app, "/api/projects").methods("GET"_method)(getProjects);
        CROW_ROUTE(app, "/api/customers").methods("GET"_method)(getCustomers);
        CROW_ROUTE(app, "/api/users").methods("GET"_method)(getUsers);
        CROW_ROUTE(app, "/api/holidays").methods("GET"_method)(getHolidays);
        CROW_ROUTE(app, "/api/holidays/year/<int>").methods("GET"_method)(getHolidaysByYear);

        CROW_ROUTE(app, "/api/workhours/range").methods("GET"_method)(getWorkHoursByRange);
        CROW_ROUTE(app, "/api/workhours/submit").methods("POST"_method)(submitWorkHour);
        CROW_ROUTE(app, "/api/statistics/workhours").methods("GET"_method)(getWorkHourStatistics);

        CROW_ROUTE(app, "/api/sync/status").methods("GET"_method)(getSyncStatus);
        CROW_ROUTE(app, "/api/db/tables").methods("GET"_method)(getDbTables);
        CROW_ROUTE(app, "/api/db/query").methods("POST"_method)(executeDbQuery);
        CROW_ROUTE(app, "/api/db/table/<string>").methods("GET"_method)(getTableData);
        CROW_ROUTE(app, "/api/config").methods("GET"_method)(getConfig);
        CROW_ROUTE(app, "/api/config").methods("POST"_method)(saveConfig);
        CROW_ROUTE(app, "/api/restart").methods("POST"_method)(restartApplication);
    }

private:
    // Static member to store  instance
    static nlohmann::json createResponse(int code, const std::string &message, const nlohmann::json &data = nlohmann::json::object())
    {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);

        std::stringstream ss;
        ss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%SZ");

        return nlohmann::json{ { "code", code }, { "message", message }, { "data", data }, { "timestamp", ss.str() } };
    }

    static crow::response jsonResponse(const nlohmann::json &json, int status_code = 200)
    {
        crow::response res(status_code);
        res.set_header("Content-Type", "application/json");
        res.set_header("Access-Control-Allow-Origin", "*");
        res.set_header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        res.set_header("Access-Control-Allow-Headers", "Content-Type, Authorization");
        res.body = json.dump();
        return res;
    }

    static crow::response getProducts(const crow::request &req)
    {
        try
        {
            auto items = app->getProductDao().findAll();
            auto response = createResponse(0, "success", items);
            return jsonResponse(response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            return jsonResponse(response, 500);
        }
    }

    static crow::response getProjects(const crow::request &req)
    {
        try
        {
            auto items = app->getProjectDao().findAll();
            auto response = createResponse(0, "success", items);
            return jsonResponse(response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            return jsonResponse(response, 500);
        }
    }

    static crow::response getCustomers(const crow::request &req)
    {
        try
        {
            auto items = app->getCustomerDao().findAll();
            auto response = createResponse(0, "success", items);
            return jsonResponse(response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            return jsonResponse(response, 500);
        }
    }

    static crow::response getUsers(const crow::request &req)
    {
        try
        {
            auto items = app->getUserDao().findAll();
            auto response = createResponse(0, "success", items);
            return jsonResponse(response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            return jsonResponse(response, 500);
        }
    }

    static crow::response getHolidays(const crow::request &req)
    {
        try
        {
            auto items = app->getHolidayDao().findAll();
            auto response = createResponse(0, "success", items);
            return jsonResponse(response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            return jsonResponse(response, 500);
        }
    }

    static crow::response getHolidaysByYear(const crow::request &req, int year)
    {
        try
        {
            auto &dao = app->getHolidayDao();
            auto holidays = dao.findByYear(year);
            auto response = createResponse(0, "success", holidays);
            return jsonResponse(response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            return jsonResponse(response, 500);
        }
    }

    static crow::response getSyncStatus(const crow::request &req)
    {
        try
        {
            auto &service = app->getSyncService();
            auto statuses = service.getSyncStatus();
            auto response = createResponse(0, "success", statuses);
            return jsonResponse(response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            return jsonResponse(response, 500);
        }
    }

    static crow::response getWorkHoursByRange(const crow::request &req)
    {
        try
        {
            std::string startDate = req.url_params.get("start_date") ? req.url_params.get("start_date") : "";
            std::string endDate = req.url_params.get("end_date") ? req.url_params.get("end_date") : "";

            if (startDate.empty() || endDate.empty())
            {
                auto response = createResponse(1, "start_date and end_date parameters are required");
                return jsonResponse(response, 400);
            }

            auto &dao = app->getWorkHourDao();
            auto workHours = dao.findByDateRange(startDate, endDate);
            auto response = createResponse(0, "success", workHours);
            return jsonResponse(response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            return jsonResponse(response, 500);
        }
    }

    static crow::response submitWorkHour(const crow::request &req)
    {
        try
        {
            if (req.body.empty())
            {
                auto response = createResponse(1, "Request body is required");
                return jsonResponse(response, 400);
            }

            auto workHourData = nlohmann::json::parse(req.body);

            // Validate required fields
            if (!workHourData.contains("employeeId") || !workHourData.contains("employeeName") || !workHourData.contains("submitDate") ||
                !workHourData.contains("timeConsumedList"))
            {
                auto response = createResponse(1, "Missing required fields: employeeId, employeeName, submitDate, timeConsumedList");
                return jsonResponse(response, 400);
            }

            auto &service = app->getSyncService();
            bool success = service.submitWorkHour(workHourData);

            auto response = createResponse(success ? 0 : 1, success ? "Work hour submitted successfully" : "Failed to submit work hour");
            return jsonResponse(response, success ? 200 : 500);
        }
        catch (const nlohmann::json::parse_error &e)
        {
            auto response = createResponse(1, "Invalid JSON format: " + std::string(e.what()));
            return jsonResponse(response, 400);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            return jsonResponse(response, 500);
        }
    }

    static crow::response getWorkHourStatistics(const crow::request &req)
    {
        try
        {
            std::string startDate = req.url_params.get("start_date") ? req.url_params.get("start_date") : "";
            std::string endDate = req.url_params.get("end_date") ? req.url_params.get("end_date") : "";

            if (startDate.empty() || endDate.empty())
            {
                auto response = createResponse(1, "start_date and end_date parameters are required");
                return jsonResponse(response, 400);
            }

            auto &dao = app->getWorkHourDao();
            auto statistics = dao.getStatistics(startDate, endDate);

            auto response = createResponse(0, "success", statistics);
            return jsonResponse(response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            return jsonResponse(response, 500);
        }
    }

    static crow::response getDbTables(const crow::request &req)
    {
        try
        {
            auto &db = app->getDatabaseManager().getDatabase();
            SQLite::Statement query(db, "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name");

            nlohmann::json tables = nlohmann::json::array();
            while (query.executeStep())
            {
                std::string tableName = query.getColumn(0).getString();
                tables.push_back(tableName);
            }

            auto response = createResponse(0, "success", tables);
            return jsonResponse(response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            return jsonResponse(response, 500);
        }
    }

    static crow::response executeDbQuery(const crow::request &req)
    {
        try
        {
            auto json = nlohmann::json::parse(req.body);
            std::string sql = json.value("sql", "");

            if (sql.empty())
            {
                auto response = createResponse(1, "SQL query is required");
                return jsonResponse(response, 400);
            }

            auto &db = app->getDatabaseManager().getDatabase();

            // Check if it's a SELECT query (read-only for safety)
            std::string sqlUpper = sql;
            std::transform(sqlUpper.begin(), sqlUpper.end(), sqlUpper.begin(), ::toupper);
            if (sqlUpper.find("SELECT") != 0)
            {
                auto response = createResponse(1, "Only SELECT queries are allowed");
                return jsonResponse(response, 400);
            }

            SQLite::Statement query(db, sql);

            nlohmann::json result = nlohmann::json::object();
            result["columns"] = nlohmann::json::array();
            result["rows"] = nlohmann::json::array();

            // Get column names
            int columnCount = query.getColumnCount();
            for (int i = 0; i < columnCount; i++)
            {
                result["columns"].push_back(query.getColumnName(i));
            }

            // Get data rows
            while (query.executeStep())
            {
                nlohmann::json row = nlohmann::json::array();
                for (int i = 0; i < columnCount; i++)
                {
                    if (query.getColumn(i).isNull())
                    {
                        row.push_back(nullptr);
                    }
                    else
                    {
                        row.push_back(query.getColumn(i).getString());
                    }
                }
                result["rows"].push_back(row);
            }

            auto response = createResponse(0, "success", result);
            return jsonResponse(response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            return jsonResponse(response, 500);
        }
    }

    static crow::response getTableData(const crow::request &req, const std::string &tableName)
    {
        try
        {
            auto &db = app->getDatabaseManager().getDatabase();

            // Validate table name exists
            SQLite::Statement checkQuery(db, "SELECT name FROM sqlite_master WHERE type='table' AND name=?");
            checkQuery.bind(1, tableName);

            if (!checkQuery.executeStep())
            {
                auto response = createResponse(1, "Table not found");
                return jsonResponse(response, 404);
            }

            // Get table data
            std::string sql = "SELECT * FROM " + tableName;
            SQLite::Statement query(db, sql);

            nlohmann::json result = nlohmann::json::object();
            result["columns"] = nlohmann::json::array();
            result["rows"] = nlohmann::json::array();

            // Get column names
            int columnCount = query.getColumnCount();
            for (int i = 0; i < columnCount; i++)
            {
                result["columns"].push_back(query.getColumnName(i));
            }

            // Get data rows
            while (query.executeStep())
            {
                nlohmann::json row = nlohmann::json::array();
                for (int i = 0; i < columnCount; i++)
                {
                    if (query.getColumn(i).isNull())
                    {
                        row.push_back(nullptr);
                    }
                    else
                    {
                        row.push_back(query.getColumn(i).getString());
                    }
                }
                result["rows"].push_back(row);
            }

            auto response = createResponse(0, "success", result);
            return jsonResponse(response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            return jsonResponse(response, 500);
        }
    }

    static crow::response getConfig(const crow::request &req)
    {
        try
        {
            std::ifstream configFile("config.json");
            if (!configFile.is_open())
            {
                auto response = createResponse(1, "Cannot open config.json");
                return jsonResponse(response, 500);
            }

            nlohmann::json config;
            configFile >> config;
            configFile.close();

            auto response = createResponse(0, "success", config);
            return jsonResponse(response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            return jsonResponse(response, 500);
        }
    }

    static crow::response saveConfig(const crow::request &req)
    {
        try
        {
            if (req.body.empty())
            {
                auto response = createResponse(1, "Request body is required");
                return jsonResponse(response, 400);
            }

            auto newConfig = nlohmann::json::parse(req.body);

            // Validate required fields (holiday_api is optional)
            if (!newConfig.contains("server") || !newConfig.contains("database") || !newConfig.contains("backend") || !newConfig.contains("sync"))
            {
                auto response = createResponse(1, "Missing required configuration sections");
                return jsonResponse(response, 400);
            }

            // Write to config file
            std::ofstream outFile("config.json");
            if (!outFile.is_open())
            {
                auto response = createResponse(1, "Cannot open config.json for writing");
                return jsonResponse(response, 500);
            }

            outFile << newConfig.dump(4);
            outFile.close();

            auto response = createResponse(0, "Configuration saved successfully");
            return jsonResponse(response);
        }
        catch (const nlohmann::json::parse_error &e)
        {
            auto response = createResponse(1, "Invalid JSON format: " + std::string(e.what()));
            return jsonResponse(response, 400);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            return jsonResponse(response, 500);
        }
    }

    static crow::response restartApplication(const crow::request &req)
    {
        try
        {
            auto response = createResponse(0, "Application restart initiated");
            app.reset(new Application);
            return jsonResponse(response);
        }
        catch (const std::exception &e)
        {
            auto response = createResponse(1, "Error: " + std::string(e.what()));
            return jsonResponse(response, 500);
        }
    }
};
