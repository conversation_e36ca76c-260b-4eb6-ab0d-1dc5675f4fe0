{"server": {"host": "0.0.0.0", "port": 8080, "threads": 4}, "database": {"path": "./data/dailyreport.db", "connection_pool_size": 10, "enable_wal_mode": true, "busy_timeout": 30000}, "backend": {"base_url": "http://***************:7000", "timeout": 60, "retry_count": 3, "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJneCIsInVzZXJfaWQiOjYyNjc4MjY5MTU3MTgwMjUyOCwiZXhwIjoxNzQ5NDU4OTAwfQ.Bz5iiEbA1kWg9BLTYzfAF1HVNvdpDifFVzQSh08a3Ts", "cookie": "SESSION=OTNkNWRhNGEtZGViNi00MzQxLWIzNWEtN2JjMGQ3ZDg4MjQ1; token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOjE1LCJjcmVhdGVkIjoxNzQ5Nzk0ODAxOTU0LCJleHAiOjE3NDk4ODEyMDF9.u5gZnzuTrrb3vsxbwWrqGyFyYplgfBHbKvuVGS1bKjo"}, "sync": {"interval_minutes": 60, "auto_start": true}, "holiday": {"source": "https://github.do/raw.githubusercontent.com/NateScarlet/holiday-cn/master/{}.json", "timeout": {"connection": 20, "read": 30, "write": 10}, "retry_count": 3}}