{"server": {"host": "0.0.0.0", "port": 8080, "threads": 4}, "database": {"path": "./data/dailyreport.db", "connection_pool_size": 10, "enable_wal_mode": true, "busy_timeout": 30000}, "backend": {"base_url": "http://***************:7000", "timeout": 60, "retry_count": 3, "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJneCIsInVzZXJfaWQiOjYyNjc4MjY5MTU3MTgwMjUyOCwiZXhwIjoxNzQ5NDU4OTAwfQ.Bz5iiEbA1kWg9BLTYzfAF1HVNvdpDifFVzQSh08a3Ts"}, "sync": {"interval_minutes": 60, "auto_start": true}, "holiday_api": {"source": "https://github.do/raw.githubusercontent.com/NateScarlet/holiday-cn/master/{year}.json", "timeout": {"connection": 20, "read": 30, "write": 10}, "retry_count": 3}}