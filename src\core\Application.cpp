#include "core/Application.hpp"
#include "controllers/ApiController.hpp"
#include "controllers/StaticController.hpp"
#include "dao/CustomerDao.hpp"
#include "dao/HolidayDao.hpp"
#include "dao/ProductDao.hpp"
#include "dao/ProjectDao.hpp"
#include "dao/SyncStatusDao.hpp"
#include "dao/UserDao.hpp"
#include "dao/WorkHourDao.hpp"
#include "services/SyncService.hpp"
#include <spdlog/spdlog.h>

#ifdef _WIN32
    #include <fcntl.h>
    #include <io.h>
    #include <process.h>
    #include <windows.h>
#else
    #include <unistd.h>
#endif

Application::Application(const std::string &config_file)
    : config_file_(config_file)
    , running_(false)
{
}

Application::~Application()
{
    stop();
    if (sync_service_ && sync_service_->isRunning())
    {
        sync_service_->stopAutoSync();
    }

    database_manager_.close();
}

bool Application::initialize()
{
    spdlog::info("Initializing Daily Report System...");

    {
        std::ifstream configFile("config.json");
        config_ = nlohmann::json::parse(configFile);
    }

    if (!database_manager_.initialize(config_.database.path))
    {
        spdlog::error("Failed to initialize database");
        return false;
    }

    // Initialize DAO components after database is ready
    initializeDaos();

    spdlog::info("Application initialized successfully");
    return true;
}

void Application::start()
{
    if (initialize())
    {
        setupRoutes();

        spdlog::info("Application: Starting synchronization service...");
        sync_service_->startAutoSync();
        spdlog::info("Application: Synchronization service started");

        startServer();
    }
}

void Application::stop()
{
    if (running_)
    {
        running_ = false;
        server_.stop();
        if (server_thread_.joinable())
        {
            server_thread_.join();
        }
        spdlog::info("Application: Server stopped");
    }
}

void Application::setupRoutes()
{
    ApiController::setupRoutes(server_);
    StaticController::setupRoutes(server_);
}

void Application::startServer()
{
    running_ = true;
    spdlog::info("Application: Starting server on {}:{}", config_.server.host, config_.server.port);

    server_thread_ = std::thread([this]() {
        if (!server_.listen(config_.server.host, config_.server.port))
        {
            spdlog::error("Application: Failed to start server on {}:{}", config_.server.host, config_.server.port);
            running_ = false;
        }
    });

    spdlog::info("Application: Server started successfully");

    // Wait for server thread to complete
    if (server_thread_.joinable())
    {
        server_thread_.join();
    }
}

void Application::initializeDaos()
{
    spdlog::info("Application: Initializing DAO components...");

    // Initialize DAO components with database manager reference
    product_dao_ = std::make_shared<ProductDao>(database_manager_);
    project_dao_ = std::make_shared<ProjectDao>(database_manager_);
    customer_dao_ = std::make_shared<CustomerDao>(database_manager_);
    user_dao_ = std::make_shared<UserDao>(database_manager_);
    work_hour_dao_ = std::make_shared<WorkHourDao>(database_manager_);
    holiday_dao_ = std::make_shared<HolidayDao>(database_manager_);
    sync_status_dao_ = std::make_shared<SyncStatusDao>(database_manager_);

    // Initialize SyncService with Application reference and all dependencies
    sync_service_ = std::make_shared<SyncService>(*this);

    spdlog::info("Application: DAO components and SyncService initialized");
}
