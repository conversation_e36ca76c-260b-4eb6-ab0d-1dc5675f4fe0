#include "core/Application.hpp"
#include "controllers/ApiController.hpp"
#include "controllers/StaticController.hpp"
#include "dao/CustomerDao.hpp"
#include "dao/HolidayDao.hpp"
#include "dao/ProductDao.hpp"
#include "dao/ProjectDao.hpp"
#include "dao/SyncStatusDao.hpp"
#include "dao/UserDao.hpp"
#include "dao/WorkHourDao.hpp"
#include "services/SyncService.hpp"
#include <spdlog/spdlog.h>

#ifdef _WIN32
    #include <fcntl.h>
    #include <io.h>
    #include <process.h>
    #include <windows.h>
#else
    #include <unistd.h>
#endif

// Custom logger to redirect Crow logs to spdlog
class SpdlogCrowHandler : public crow::ILogHandler
{
public:
    void log(std::string message, crow::LogLevel level) override
    {
        // Remove trailing newline if present
        if (!message.empty() && message.back() == '\n')
        {
            message.pop_back();
        }

        switch (level)
        {
        case crow::LogLevel::Debug:
            spdlog::debug("Crow: {}", message);
            break;
        case crow::LogLevel::Info:
            spdlog::info("Crow: {}", message);
            break;
        case crow::LogLevel::Warning:
            spdlog::warn("Crow: {}", message);
            break;
        case crow::LogLevel::Error:
            spdlog::error("Crow: {}", message);
            break;
        case crow::LogLevel::Critical:
            spdlog::critical("Crow: {}", message);
            break;
        }
    }
};

static SpdlogCrowHandler crow_logger;

Application::Application(const std::string &config_file)
    : crow::SimpleApp()
    , config_file_(config_file)
{
}

Application::~Application()
{
    stop();
    if (sync_service_ && sync_service_->isRunning())
    {
        sync_service_->stopAutoSync();
    }

    database_manager_.close();
}

bool Application::initialize()
{
    spdlog::info("Initializing Daily Report System...");

    {
        std::ifstream configFile("config.json");
        config_ = nlohmann::json::parse(configFile);
    }

    if (!database_manager_.initialize(config_.database.path))
    {
        spdlog::error("Failed to initialize database");
        return false;
    }

    // Initialize DAO components after database is ready
    initializeDaos();

    spdlog::info("Application initialized successfully");
    return true;
}

void Application::start()
{
    if (initialize())
    {
        ApiController::setupRoutes(static_cast<crow::SimpleApp &>(*app.get()));
        StaticController::setupRoutes(static_cast<crow::SimpleApp &>(*app.get()));

        spdlog::info("Application: Starting synchronization service...");
        sync_service_->startAutoSync();
        spdlog::info("Application: Synchronization service started");

        spdlog::info("Application: Starting server on port {}", config_.server.port);
        crow::logger::setHandler(&crow_logger);
        this->port(config_.server.port).multithreaded().run();
    }
}

void Application::initializeDaos()
{
    spdlog::info("Application: Initializing DAO components...");

    // Initialize DAO components with database manager reference
    product_dao_ = std::make_shared<ProductDao>(database_manager_);
    project_dao_ = std::make_shared<ProjectDao>(database_manager_);
    customer_dao_ = std::make_shared<CustomerDao>(database_manager_);
    user_dao_ = std::make_shared<UserDao>(database_manager_);
    work_hour_dao_ = std::make_shared<WorkHourDao>(database_manager_);
    holiday_dao_ = std::make_shared<HolidayDao>(database_manager_);
    sync_status_dao_ = std::make_shared<SyncStatusDao>(database_manager_);

    // Initialize SyncService with Application reference and all dependencies
    sync_service_ = std::make_shared<SyncService>(*this);

    spdlog::info("Application: DAO components and SyncService initialized");
}
