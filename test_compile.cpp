#include <httplib.h>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>
#include <iostream>

int main() {
    // Test basic httplib functionality
    httplib::Server server;
    
    server.Get("/test", [](const httplib::Request& req, httplib::Response& res) {
        nlohmann::json response = {
            {"message", "Hello from cpp-httplib!"},
            {"status", "success"}
        };
        res.set_header("Content-Type", "application/json");
        res.body = response.dump();
    });
    
    spdlog::info("Test compilation successful - CROW replaced with cpp-httplib");
    std::cout << "Test compilation successful - CROW replaced with cpp-httplib" << std::endl;
    
    return 0;
}
