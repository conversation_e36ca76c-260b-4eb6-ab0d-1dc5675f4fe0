#pragma once

#include "config/Config.hpp"
#include "database/DatabaseManager.hpp"
#include <httplib.h>
#include <memory>
#include <spdlog/spdlog.h>
#include <thread>

// Forward declarations to avoid circular dependencies
class DatabaseManager;
class ProductDao;
class ProjectDao;
class CustomerDao;
class UserDao;
class WorkHourDao;
class HolidayDao;
class SyncStatusDao;
class HttpClient;
class SyncService;
class ApiController;
class StaticController;

class Application
{
public:
    Application(const std::string &config_file = "config.json");
    ~Application();

    // Getter methods for accessing components
    Config &getConfig()
    {
        return config_;
    }
    DatabaseManager &getDatabaseManager()
    {
        return database_manager_;
    }
    SyncService &getSyncService()
    {
        return *sync_service_;
    }

    // DAO getters
    ProductDao &getProductDao()
    {
        return *product_dao_;
    }
    ProjectDao &getProjectDao()
    {
        return *project_dao_;
    }
    CustomerDao &getCustomerDao()
    {
        return *customer_dao_;
    }
    UserDao &getUserDao()
    {
        return *user_dao_;
    }
    WorkHourDao &getWorkHourDao()
    {
        return *work_hour_dao_;
    }
    HolidayDao &getHolidayDao()
    {
        return *holiday_dao_;
    }
    SyncStatusDao &getSyncStatusDao()
    {
        return *sync_status_dao_;
    }

    void start();
    void stop();
    httplib::Server& getServer() { return server_; }

private:
    bool initialize();
    void setupRoutes();
    void startServer();
    void startSyncService();
    void initializeDaos();

    std::string config_file_;
    Config config_;
    DatabaseManager database_manager_;
    httplib::Server server_;
    std::thread server_thread_;
    bool running_;

    // DAO components - initialized after database_manager_
    std::shared_ptr<ProductDao> product_dao_;
    std::shared_ptr<ProjectDao> project_dao_;
    std::shared_ptr<CustomerDao> customer_dao_;
    std::shared_ptr<UserDao> user_dao_;
    std::shared_ptr<WorkHourDao> work_hour_dao_;
    std::shared_ptr<HolidayDao> holiday_dao_;
    std::shared_ptr<SyncStatusDao> sync_status_dao_;
    std::shared_ptr<SyncService> sync_service_;
};

extern std::unique_ptr<Application> app;