#pragma once

#include <string>
#include <nlohmann/json.hpp>

struct WorkHour {
    int id = 0;
    std::string user_id;
    std::string user_name;
    int project_id = 0;
    int product_id = 0;
    int customer_id = 0;
    std::string work_date;
    std::string start_time;
    std::string end_time;
    double duration_hours = 0.0;
    std::string work_description;
    std::string work_type;
    std::string created_time;
    std::string updated_time;

    NLOHMANN_DEFINE_TYPE_INTRUSIVE(WorkHour, id, user_id, user_name, project_id, product_id, customer_id,
                                   work_date, start_time, end_time, duration_hours, work_description,
                                   work_type, created_time, updated_time)
};

struct WorkHourStatistics {
    double total_hours = 0.0;
    int total_records = 0;
    int active_users = 0;
    int active_projects = 0;

    struct UserStats {
        std::string user_id;
        std::string user_name;
        double total_hours = 0.0;
        int record_count = 0;
        int work_days = 0;

        NLOHMANN_DEFINE_TYPE_INTRUSIVE(UserStats, user_id, user_name, total_hours, record_count, work_days)
    };

    struct ProjectStats {
        int project_id = 0;
        std::string project_name;
        double total_hours = 0.0;
        int record_count = 0;
        int user_count = 0;

        NLOHMANN_DEFINE_TYPE_INTRUSIVE(ProjectStats, project_id, project_name, total_hours, record_count, user_count)
    };

    std::vector<UserStats> by_user;
    std::vector<ProjectStats> by_project;

    NLOHMANN_DEFINE_TYPE_INTRUSIVE(WorkHourStatistics, total_hours, total_records, active_users, active_projects, by_user, by_project)
};




