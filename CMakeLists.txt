﻿cmake_minimum_required(VERSION 3.20)
project(daily_report VERSION 0.1.0)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Option to embed resources
option(EMBED_RESOURCES "Embed web resources into executable" OFF)

find_package(nlohmann_json CONFIG REQUIRED)
find_package(httplib CONFIG REQUIRED)
find_package(SQLiteCpp CONFIG REQUIRED)
find_package(spdlog CONFIG REQUIRED)
find_package(OpenSSL REQUIRED)

include_directories(include)

# Enable HTTPS support for httplib
add_definitions(-DCPPHTTPLIB_OPENSSL_SUPPORT)

file(GLOB_RECURSE SRC_FILES
    "src/*.cpp"
    "include/*.hpp"
)

# Generate embedded resources if enabled
if(EMBED_RESOURCES)
    find_package(Python3 REQUIRED COMPONENTS Interpreter)

    set(EMBEDDED_RESOURCES_HEADER "${CMAKE_CURRENT_BINARY_DIR}/include/resources/EmbeddedResources.hpp")

    add_custom_command(
        OUTPUT ${EMBEDDED_RESOURCES_HEADER}
        COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_CURRENT_BINARY_DIR}/include/resources"
        COMMAND ${Python3_EXECUTABLE} "${CMAKE_CURRENT_SOURCE_DIR}/tools/embed_resources.py"
        "${CMAKE_CURRENT_SOURCE_DIR}/web"
        "${EMBEDDED_RESOURCES_HEADER}"
        DEPENDS "${CMAKE_CURRENT_SOURCE_DIR}/web/index.html"
        "${CMAKE_CURRENT_SOURCE_DIR}/web/db.html"
        COMMENT "Generating embedded resources"
        VERBATIM
    )

    add_custom_target(generate_resources DEPENDS ${EMBEDDED_RESOURCES_HEADER})

    list(APPEND SRC_FILES ${EMBEDDED_RESOURCES_HEADER})
    include_directories("${CMAKE_CURRENT_BINARY_DIR}/include")

    add_compile_definitions(EMBED_RESOURCES)
endif()

add_executable(${PROJECT_NAME} ${SRC_FILES})

if(WIN32)
    target_compile_options(${PROJECT_NAME} PRIVATE /bigobj)
endif()

if(EMBED_RESOURCES)
    add_dependencies(${PROJECT_NAME} generate_resources)
endif()

target_link_libraries(${PROJECT_NAME}
    PRIVATE
    nlohmann_json::nlohmann_json
    httplib::httplib
    SQLiteCpp
    spdlog::spdlog_header_only
    OpenSSL::SSL OpenSSL::Crypto
)

# Copy configuration file to build directory
add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    ${CMAKE_SOURCE_DIR}/config.json
    $<TARGET_FILE_DIR:${PROJECT_NAME}>/config.json
    COMMENT "Copying config.json to build directory"
)

# Copy web files only if not embedding resources
if(NOT EMBED_RESOURCES)
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_directory
        ${CMAKE_SOURCE_DIR}/web
        $<TARGET_FILE_DIR:${PROJECT_NAME}>/web
        COMMENT "Copying web directory to build directory"
    )
endif()
