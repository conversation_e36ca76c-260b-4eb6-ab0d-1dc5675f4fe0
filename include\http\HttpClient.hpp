#pragma once

#include <chrono>
#include <fstream>
#include <httplib.h>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>
#include <string>
#include <thread>

struct ApiResponse
{
    int code = -1;
    std::string message;
    nlohmann::json data;
    bool success = false;
};

class HttpClient
{
public:
    HttpClient(std::string base_url, std::string cookie, std::string refresh_token)
        : base_url_(std::move(base_url))
        , cookie_(std::move(cookie))
    {
        refreshToken(std::move(refresh_token));
    }

    void setTimeout(int timeout_seconds)
    {
        timeout_seconds_ = timeout_seconds;
    }

    void setRetryCount(int retry_count)
    {
        retry_count_ = retry_count;
    }

    ApiResponse get(const std::string &path)
    {
        return makeRequest("GET", path);
    }

    ApiResponse post(const std::string &path, const nlohmann::json &body)
    {
        return makeRequest("POST", path, body);
    }

public:
    HttpClient() = default;

    // Refresh the authentication token
    bool refreshToken(const std::string &refresh_token)
    {
        assert(!refresh_token.empty());
        spdlog::info("HttpClient: Starting token refresh...");
        try
        {
            std::string refreshUrl = "/atom/v1/atomRbacService/oauth/refreshToken?refreshToken=" + refresh_token;
            auto response = post(refreshUrl, {});
            if (!response.success)
            {
                spdlog::error("HttpClient: Token refresh API call failed - Code: {}, Message: {}", response.code, response.message);
                spdlog::error("HttpClient: Response data: {}", response.data.dump());
                return false;
            }
            auth_token_ = response.data["accessToken"];
            spdlog::info("HttpClient: Auth token updated");
            saveTokenToConfig(response.data["refreshToken"]);
            spdlog::info("HttpClient: New refresh token received, updating configuration...");

            return true;
        }
        catch (const std::exception &e)
        {
            spdlog::error("HttpClient: Token refresh exception: {}", e.what());
            return false;
        }
    }

    // Save new token to config.json file
    bool saveTokenToConfig(const std::string &newToken)
    {
        try
        {
            nlohmann::json config;
            {
                std::ifstream configFile("config.json");
                config = nlohmann::json::parse(configFile);
            }

            // Update the token
            config["backend"]["auth"]["header_value"] = newToken;

            // Write back to file
            {
                std::ofstream outFile("config.json");
                outFile << std::setw(4) << config << std::endl;
            }

            spdlog::info("HttpClient: Token saved to config.json successfully");
            return true;
        }
        catch (const std::exception &e)
        {
            spdlog::error("HttpClient: Failed to save token to config: {}", e.what());
            return false;
        }
    }

    ApiResponse makeRequest(const std::string &method, const std::string &path, const nlohmann::json &body = {})
    {
        SPDLOG_INFO("API {} {}", method, path);

        if (base_url_.empty())
        {
            ApiResponse response;
            response.message = "Base URL not set";
            SPDLOG_ERROR("HttpClient: Base URL not set");
            return response;
        }

        size_t pos = base_url_.find("://");
        if (pos == std::string::npos)
        {
            ApiResponse response;
            response.message = "Invalid base URL format";
            return response;
        }

        std::string protocol = base_url_.substr(0, pos);
        std::string host_port = base_url_.substr(pos + 3);

        pos = host_port.find('/');
        std::string host_and_port = (pos != std::string::npos) ? host_port.substr(0, pos) : host_port;
        std::string base_path = (pos != std::string::npos) ? host_port.substr(pos) : "";

        pos = host_and_port.find(':');
        std::string host = (pos != std::string::npos) ? host_and_port.substr(0, pos) : host_and_port;
        int port = (pos != std::string::npos) ? std::stoi(host_and_port.substr(pos + 1)) : (protocol == "https" ? 443 : 80);

        for (int attempt = 0; attempt < retry_count_; ++attempt)
        {
            try
            {

                httplib::Client client(host, port);
                client.set_connection_timeout(timeout_seconds_);
                client.set_read_timeout(timeout_seconds_);
                client.set_compress(true);  // Enable gzip compression

                std::string full_path = base_path + path;
                httplib::Result result;

                // Set headers for proper encoding and authentication
                httplib::Headers headers = {
                    { "Accept", "application/json, text/plain, */*" },
                    { "Accept-Encoding", "gzip, deflate" },
                    { "Accept-Language", "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7" },
                    { "Accept-Charset", "utf-8" },
                    { "Connection", "keep-alive" },
                    { "Content-Type", "application/json; charset=utf-8" },
                    { "User-Agent",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" },
                    { "Cookie", cookie_ },
                    { "Authorization", auth_token_ },
                };

                if (method == "GET")
                {
                    result = client.Get(full_path, headers);
                }
                else if (method == "POST")
                {
                    std::string json_str = body.dump();
                    result = client.Post(full_path, headers, json_str, "application/json; charset=utf-8");
                }

                if (result)
                {
                    return parseResponse(result);
                }
                else
                {
                    if (attempt < retry_count_ - 1)
                    {
                        std::this_thread::sleep_for(std::chrono::milliseconds(1000 * (attempt + 1)));
                        continue;
                    }
                }
            }
            catch (const std::exception &e)
            {
                SPDLOG_ERROR("API request error: {}", e.what());
                if (attempt < retry_count_ - 1)
                {
                    std::this_thread::sleep_for(std::chrono::milliseconds(1000 * (attempt + 1)));
                    continue;
                }
            }
        }

        ApiResponse response;
        response.message = "Request failed after " + std::to_string(retry_count_) + " attempts";
        SPDLOG_ERROR("API {} {} failed after {} attempts", method, path, retry_count_);
        return response;
    }

    ApiResponse parseResponse(const httplib::Result &result)
    {
        ApiResponse response;

        if (!result)
        {
            response.message = "No response received";
            SPDLOG_ERROR("No response received");
            return response;
        }

        spdlog::debug("HttpClient: Response status: {}, body length: {}", result->status, result->body.length());
        spdlog::debug("HttpClient: Response body: {}", result->body.substr(0, 500));  // Log first 500 chars

        try
        {
            nlohmann::json json_response = nlohmann::json::parse(result->body);

            if (json_response.contains("code"))
            {
                response.code = json_response["code"].get<int>();
            }

            if (json_response.contains("msg"))
            {
                response.message = json_response["msg"].get<std::string>();
            }
            else if (json_response.contains("message"))
            {
                response.message = json_response["message"].get<std::string>();
            }

            if (json_response.contains("data"))
            {
                response.data = json_response["data"];
            }

            response.success = (response.code == 0 || response.code == 200);

            spdlog::info("HttpClient: Response parsed - code: {}, success: {}, message: {}", response.code, response.success, response.message);
        }
        catch (const std::exception &e)
        {
            response.message = "Failed to parse JSON response: " + std::string(e.what());
            response.success = false;
            spdlog::error("HttpClient: JSON parse error: {}", e.what());
        }

        return response;
    }

private:
    std::string base_url_;
    std::string cookie_;
    std::string auth_token_;
    int timeout_seconds_ = 30;
    int retry_count_ = 3;
};
