#pragma once

#include "models/Customer.hpp"
#include "database/DatabaseManager.hpp"
#include <vector>
#include <optional>
#include <spdlog/spdlog.h>

class CustomerDao {
public:
    CustomerDao(DatabaseManager& db_manager) : db_manager_(db_manager) {}
    
    bool insert(const Customer& customer) {
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, R"(
                INSERT OR REPLACE INTO customers (id, unit_name, contact, department, phones, group_name)
                VALUES (?, ?, ?, ?, ?, ?)
            )");
            
            query.bind(1, customer.id);
            query.bind(2, customer.unit_name);
            query.bind(3, customer.contact);
            query.bind(4, customer.department);
            query.bind(5, customer.phones);
            query.bind(6, customer.group_name);
            
            return query.exec() > 0;
        } catch (const std::exception& e) {
            SPDLOG_ERROR("CustomerDao::insert error: {}", e.what());
            return false;
        }
    }
    
    std::vector<Customer> findAll() {
        std::vector<Customer> customers;
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, "SELECT * FROM customers ORDER BY id");
            
            while (query.executeStep()) {
                Customer customer;
                customer.id = query.getColumn(0).getInt();
                customer.unit_name = query.getColumn(1).getString();
                customer.contact = query.getColumn(2).getString();
                customer.department = query.getColumn(3).getString();
                customer.phones = query.getColumn(4).getString();
                customer.group_name = query.getColumn(5).getString();
                customers.push_back(customer);
            }
        } catch (const std::exception& e) {
            spdlog::error("CustomerDao::findAll error: {}", e.what());
        }
        return customers;
    }
    
    bool insertOrUpdate(const Customer& customer) {
        return insert(customer);
    }

    bool batchInsertOrUpdate(const std::vector<Customer>& customers) {
        if (customers.empty()) {
            return true;
        }

        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Transaction transaction(db);

            int processed = 0;
            for (const auto& customer : customers) {
                if (insertOrUpdate(customer)) {
                    processed++;
                }
            }

            transaction.commit();
            return processed == customers.size();

        } catch (const std::exception& e) {
            SPDLOG_ERROR("CustomerDao::batchInsertOrUpdate error: {}", e.what());
            return false;
        }
    }

private:
    DatabaseManager& db_manager_;
};
