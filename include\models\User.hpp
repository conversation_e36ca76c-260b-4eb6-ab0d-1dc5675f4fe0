#pragma once

#include <nlohmann/json.hpp>
#include <string>

struct User
{
    std::string id;
    std::string account;
    std::string name;
    std::string gender;
    std::string mobile;
    std::string email;
    std::string phone;
    std::string post;
    std::string type;
    bool enable = true;
    std::string dept_name;
    std::string team_name;
    std::string role_name;

    NLOHMANN_DEFINE_TYPE_INTRUSIVE(User, id, account, name, gender, mobile, email, phone, post, type, enable, dept_name, team_name, role_name)
};

struct UserApiResponse
{
    int code = 0;
    std::string msg;
    
    struct UserData
    {
        int pageNo = 0;
        int pageSize = 50;
        int totalPage = 1;
        int total = 0;
        std::vector<User> list;

        NLOHMANN_DEFINE_TYPE_INTRUSIVE(UserData, pageNo, pageSize, totalPage, total, list)
    };
    
    UserData data;

    NLOHMANN_DEFINE_TYPE_INTRUSIVE(UserApiResponse, code, msg, data);
};

