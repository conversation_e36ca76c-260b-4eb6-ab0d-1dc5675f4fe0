#pragma once

#include <string>
#include <nlohmann/json.hpp>

struct Project {
    int id = 0;
    std::string project_code;
    std::string project_name;
    std::string short_name;
    std::string start_time;
    std::string end_time;
    std::string plan_start_time;
    std::string plan_end_time;
    std::string duty_persons;
    std::string duty_departments;
    std::string join_departments;
    std::string join_persons;
    std::string project_type;
    std::string project_state;
    std::string rel_product_list;
    int rel_customer_info = 0;
    std::string project_comment;
    std::string contract_name;

    NLOHMANN_DEFINE_TYPE_INTRUSIVE(Project, id, project_code, project_name, short_name,
                                   start_time, end_time, plan_start_time, plan_end_time,
                                   duty_persons, duty_departments, join_departments, join_persons,
                                   project_type, project_state, rel_product_list, rel_customer_info,
                                   project_comment, contract_name)
};
