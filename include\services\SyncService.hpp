#pragma once

#include "config/Config.hpp"
#include "dao/CustomerDao.hpp"
#include "dao/HolidayDao.hpp"
#include "dao/ProductDao.hpp"
#include "dao/ProjectDao.hpp"
#include "dao/SyncStatusDao.hpp"
#include "dao/UserDao.hpp"
#include "dao/WorkHourDao.hpp"
#include "http/HttpClient.hpp"
#include "models/Customer.hpp"
#include "models/Holiday.hpp"
#include "models/Product.hpp"
#include "models/Project.hpp"
#include "models/SyncStatus.hpp"
#include "models/User.hpp"
#include "models/WorkHour.hpp"
#include <algorithm>
#include <asio.hpp>
#include <atomic>
#include <chrono>
#include <ctime>
#include <httplib.h>
#include <iomanip>
#include <map>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>
#include <sstream>
#include <string>
#include <thread>
#include <vector>

class SyncService
{
public:
    SyncService(Application &application)
        : application_(application)
        , config_(application_.getConfig().backend)
        , http_client_(config_.base_url, config_.refresh_token, config_.cookie)
    {
        spdlog::info("Application: Configuring HTTP client with base URL: {}", config_.base_url);
        http_client_.setTimeout(config_.timeout);
        http_client_.setRetryCount(config_.retry_count);

checkAndSyncHolidays();
    }

    void startAutoSync()
    {
        if (auto_sync_running_)
        {
            spdlog::warn("SyncService: Auto sync already running, skipping start");
            return;
        }

        spdlog::info("SyncService: Starting automatic synchronization service with ASIO timers");
        auto_sync_running_ = true;

        // Initialize ASIO components
        io_context_ = std::make_unique<asio::io_context>();
        unified_sync_timer_ = std::make_unique<asio::steady_timer>(*io_context_);

        // Initial sync on startup - sync all data types
        spdlog::info("SyncService: Starting initial complete synchronization...");
        bool initialSyncSuccess = syncAll();
        spdlog::info("SyncService: Initial data synchronization completed with status: {}", initialSyncSuccess ? "SUCCESS" : "FAILED");

        bool workHourSyncSuccess = performInitialWorkHourSync();
        spdlog::info("SyncService: Initial WorkHour synchronization completed with status: {}", workHourSyncSuccess ? "SUCCESS" : "FAILED");

        // Start unified timer-based synchronization (12 hours interval)
        startUnifiedSyncTimer();

        // Start IO context in background thread
        io_thread_ = std::make_unique<std::thread>([this]() {
            spdlog::info("SyncService: ASIO IO thread started");
            try
            {
                io_context_->run();
            }
            catch (const std::exception &e)
            {
                spdlog::error("SyncService: ASIO IO thread exception: {}", e.what());
            }
            spdlog::info("SyncService: ASIO IO thread stopped");
        });
    }

    bool isRunning() const
    {
        return auto_sync_running_;
    }

    void stopAutoSync()
    {
        auto_sync_running_ = false;

        // Cancel timer
        if (unified_sync_timer_)
        {
            unified_sync_timer_->cancel();
        }

        // Stop IO context
        if (io_context_)
        {
            io_context_->stop();
        }

        // Join threads
        if (auto_sync_thread_.joinable())
        {
            auto_sync_thread_.join();
        }
        if (io_thread_ && io_thread_->joinable())
        {
            io_thread_->join();
        }

        // Reset ASIO components
        unified_sync_timer_.reset();
        io_context_.reset();
        io_thread_.reset();
    }

    bool syncProducts()
    {
        spdlog::info("SyncService: Starting product synchronization");
        try
        {
            auto response = http_client_.get("/atom/v1/workhour/api/product/list");

            if (!response.success)
            {
                if (response.code == 401)
                {
                    spdlog::error("SyncService: Product sync failed - Authentication required (401). Please check your authentication configuration.");
                }
                else
                {
                    spdlog::error("SyncService: Product sync failed - Code: {}, Message: {}", response.code, response.message);
                }
                return false;
            }

            auto products = parseProducts(response.data);
            spdlog::info("SyncService: Parsed {} products from API response", products.size());

            auto &dao = application_.getProductDao();
            int record_count = 0;

            for (const auto &product : products)
            {
                if (dao.insertOrUpdate(product))
                {
                    record_count++;
                }
            }

            spdlog::info("SyncService: Product sync completed - {} records processed", record_count);
            recordSyncStatus("product");
            return true;
        }
        catch (const std::exception &e)
        {
            spdlog::error("SyncService: Product sync exception - {}", e.what());
            return false;
        }
    }

    bool syncProjects()
    {
        spdlog::info("SyncService: Starting project synchronization");
        try
        {
            auto response = http_client_.get("/atom/v1/workhour/api/project/list");

            if (!response.success)
            {
                if (response.code == 401)
                {
                    spdlog::error("SyncService: Project sync failed - Authentication required (401). Please check your authentication configuration.");
                }
                else
                {
                    spdlog::error("SyncService: Project sync failed - Code: {}, Message: {}", response.code, response.message);
                }
                return false;
            }

            auto projects = parseProjects(response.data);
            spdlog::info("SyncService: Parsed {} projects from API response", projects.size());

            auto &dao = application_.getProjectDao();
            int record_count = 0;

            for (const auto &project : projects)
            {
                if (dao.insertOrUpdate(project))
                {
                    record_count++;
                }
            }

            spdlog::info("SyncService: Project sync completed - {} records processed", record_count);
            recordSyncStatus("project");
            return true;
        }
        catch (const std::exception &e)
        {
            spdlog::error("SyncService: Project sync exception - {}", e.what());
            return false;
        }
    }

    bool syncCustomers()
    {
        spdlog::info("SyncService: Starting customer synchronization");
        try
        {
            auto response = http_client_.get("/atom/v1/workhour/api/customer/list");

            if (!response.success)
            {
                if (response.code == 401)
                {
                    spdlog::error("SyncService: Customer sync failed - Authentication required (401). Please check your authentication configuration.");
                }
                else
                {
                    spdlog::error("SyncService: Customer sync failed - Code: {}, Message: {}", response.code, response.message);
                }
                return false;
            }

            auto customers = parseCustomers(response.data);
            spdlog::info("SyncService: Parsed {} customers from API response", customers.size());

            auto &dao = application_.getCustomerDao();
            int record_count = 0;

            for (const auto &customer : customers)
            {
                if (dao.insertOrUpdate(customer))
                {
                    record_count++;
                }
            }

            spdlog::info("SyncService: Customer sync completed - {} records processed", record_count);
            recordSyncStatus("customer");
            return true;
        }
        catch (const std::exception &e)
        {
            spdlog::error("SyncService: Customer sync exception - {}", e.what());
            return false;
        }
    }

    bool syncUsers()
    {
        spdlog::info("SyncService: Starting user synchronization");
        try
        {
            auto response = http_client_.get("/atom/v1/atomRbacService/user/listPage");

            if (!response.success)
            {
                if (response.code == 401)
                {
                    spdlog::error("SyncService: User sync failed - Authentication required (401). Please check your authentication configuration.");
                }
                else
                {
                    spdlog::error("SyncService: User sync failed - Code: {}, Message: {}", response.code, response.message);
                }
                return false;
            }

            auto users = parseUsers(response.data);
            spdlog::info("SyncService: Parsed {} users from API response", users.size());

            auto &dao = application_.getUserDao();
            int record_count = 0;

            for (const auto &user : users)
            {
                if (dao.insertOrUpdate(user))
                {
                    record_count++;
                }
            }

            spdlog::info("SyncService: User sync completed - {} records processed", record_count);
            recordSyncStatus("user");
            return true;
        }
        catch (const std::exception &e)
        {
            spdlog::error("SyncService: User sync exception - {}", e.what());
            return false;
        }
    }

    bool syncWorkHours(long long startTime = 0, long long endTime = 0)
    {
        spdlog::info("SyncService: Starting work hour synchronization");
        try
        {
            // If no time range specified, use default range (last 7 days)
            if (startTime == 0 || endTime == 0)
            {
                auto now = std::chrono::system_clock::now();
                auto week_ago = now - std::chrono::hours(24 * 7);
                startTime = std::chrono::duration_cast<std::chrono::milliseconds>(week_ago.time_since_epoch()).count();
                endTime = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
                spdlog::info("SyncService: Using default time range - start: {}, end: {}", startTime, endTime);
            }
            else
            {
                spdlog::info("SyncService: Using specified time range - start: {}, end: {}", startTime, endTime);
            }

            std::string endpoint = "/atom/v1/workhour/api/task/listMySubmitTaskWorkHour?pageSize=5000&startTime=" + std::to_string(startTime) +
                                   "&endTime=" + std::to_string(endTime);
            spdlog::info("SyncService: Requesting work hours from endpoint: {}", endpoint);
            auto response = http_client_.get(endpoint);

            if (!response.success)
            {
                if (response.code == 401)
                {
                    spdlog::error("SyncService: Work hour sync failed - Authentication required (401). Please check your authentication configuration.");
                }
                else
                {
                    spdlog::error("SyncService: Work hour sync failed - Code: {}, Message: {}", response.code, response.message);
                }
                return false;
            }

            auto workHours = parseWorkHours(response.data);
            spdlog::info("SyncService: Parsed {} work hours from API response", workHours.size());

            auto &dao = application_.getWorkHourDao();

            // Use batch insert/update for better performance
            bool success = dao.batchInsertOrUpdate(workHours);
            int record_count = success ? workHours.size() : 0;

            spdlog::info("SyncService: Work hour sync completed - {} records processed", record_count);
            recordSyncStatus("workhour", endTime);
            return true;
        }
        catch (const std::exception &e)
        {
            spdlog::error("SyncService: Work hour sync exception - {}", e.what());
            return false;
        }
    }

    bool syncAll()
    {
        spdlog::info("SyncService: Starting complete data synchronization");
        bool success = true;

        success &= syncProducts();
        success &= syncProjects();
        success &= syncCustomers();
        success &= syncUsers();

        spdlog::info("SyncService: Complete synchronization finished with status: {}", success ? "SUCCESS" : "FAILED");
        return success;
    }

    bool syncHolidays(int year)
    {
        spdlog::info("SyncService: Starting holiday synchronization for year {}", year);

        try
        {
            // Get holiday API configuration
            const auto &holidayApiConfig = application_.getConfig().holiday;

            nlohmann::json response_data;
            bool dataFetched = false;

            const auto &url = holidayApiConfig.source;
            spdlog::info("SyncService: Attempting to fetch holiday data from source {} ", url);

            try
            {
                // Parse URL to extract host and path
                // std::string host, path;
                auto slash = url.find('/', 8);
                auto host = url.substr(0, slash);
                auto path = url.substr(slash);

                // Create HTTP client with configuration-based settings
                httplib::Client client(host);
                client.set_connection_timeout(holidayApiConfig.timeout.connection);
                client.set_read_timeout(holidayApiConfig.timeout.read);
                client.set_write_timeout(holidayApiConfig.timeout.write);
                client.set_compress(true);
                client.set_follow_location(true);  // Follow redirects
                auto result = client.Get(path);
                if (!result)
                {
                    spdlog::warn("SyncService: Failed to connect to {}", host);
                    return false;
                }

                if (result->status != 200)
                {
                    spdlog::warn("SyncService: {} returned status {} for year {}", host, result->status, year);
                    return false;
                }

                // Try to parse JSON response
                try
                {
                    response_data = nlohmann::json::parse(result->body);
                    dataFetched = true;
                    spdlog::info("SyncService: Successfully fetched holiday data from {}", host);
                }
                catch (const std::exception &e)
                {
                    spdlog::warn("SyncService: Failed to parse JSON from {}: {}", host, e.what());
                    return false;
                }
            }
            catch (const std::exception &e)
            {
                spdlog::warn("SyncService: Exception when fetching from {}: {}", url, e.what());
                return false;
            }

            // If all external sources failed, try to use fallback data
            if (!dataFetched)
            {
                spdlog::warn("SyncService: All external sources failed, attempting to use fallback data for year {}", year);
                if (!response_data.empty())
                {
                    dataFetched = true;
                    spdlog::info("SyncService: Using fallback holiday data for year {}", year);
                }
            }

            if (!dataFetched)
            {
                spdlog::error("SyncService: Failed to fetch holiday data from all sources for year {}", year);
                return false;
            }

            auto holidays = parseHolidays(response_data);
            spdlog::info("SyncService: Parsed {} holidays from API response for year {}", holidays.size(), year);

            // Check if holidays data is empty - consider this as sync failure (no retry needed)
            if (holidays.empty())
            {
                spdlog::warn("SyncService: Holiday sync failed for year {} - no holiday data found (empty days array), skipping retries", year);
                return true;
            }

            auto &dao = application_.getHolidayDao();

            // Clear existing data for this year first
            dao.deleteByYear(year);

            // Use batch insert for better performance
            bool success = dao.batchInsert(holidays);
            int record_count = success ? holidays.size() : 0;

            if (success)
            {
                spdlog::info("SyncService: Holiday sync completed for year {} - {} records processed", year, record_count);
                recordSyncStatus("holiday_" + std::to_string(year));
                return true;
            }
            else
            {
                spdlog::error("SyncService: Holiday sync failed for year {} - database insert failed", year);
                return false;
            }
        }
        catch (const std::exception &e)
        {
            spdlog::error("SyncService: Holiday sync exception for year {} - {}", year, e.what());
            return false;
        }
    }


    std::vector<SyncStatus> getSyncStatus()
    {
        return application_.getSyncStatusDao().findAll();
    }

    SyncStatus getLastSyncStatus(const std::string &data_type)
    {
        auto result = application_.getSyncStatusDao().findByDataType(data_type);
        if (result.has_value())
        {
            return result.value();
        }

        SyncStatus empty_status;
        empty_status.data_type = data_type;
        return empty_status;
    }

    // Submit work hour to remote API
    bool submitWorkHour(const nlohmann::json &workHourData)
    {
        spdlog::info("SyncService: Starting work hour submission");
        spdlog::debug("SyncService: Work hour data: {}", workHourData.dump());

        try
        {
            auto response = http_client_.post("/atom/v1/workhour/api/task/submitWorkHour", workHourData);

            spdlog::info("SyncService: Work hour submission response - Success: {}, Code: {}, Message: {}", response.success, response.code, response.message);

            if (!response.success)
            {
                if (response.code == 401)
                {
                    spdlog::error("SyncService: Work hour submission failed - Authentication required (401). Please check your authentication configuration.");
                }
                else if (response.code == 0)
                {
                    spdlog::error("SyncService: Work hour submission failed - Network error or connection failed");
                }
                else
                {
                    spdlog::error("SyncService: Work hour submission failed - Code: {}, Message: {}", response.code, response.message);
                    spdlog::error("SyncService: Response data: {}", response.data.dump());
                }
                return false;
            }

            // Check if the response data indicates success
            if (!response.data.empty())
            {
                try
                {
                    if (response.data.contains("code"))
                    {
                        int responseCode = response.data["code"];
                        if (responseCode != 0 && responseCode != 200)
                        {
                            std::string responseMsg = response.data.value("msg", "Unknown error");
                            spdlog::error("SyncService: Work hour submission failed - API returned error code: {}, message: {}", responseCode, responseMsg);
                            return false;
                        }
                    }
                }
                catch (const std::exception &e)
                {
                    spdlog::warn("SyncService: Could not parse response data: {}", e.what());
                }
            }

            spdlog::info("SyncService: Work hour submission completed successfully");

            // Immediately sync WorkHour data to ensure user can see submitted data
            spdlog::info("SyncService: Starting immediate WorkHour sync after successful submission...");
            performIncrementalWorkHourSync();
            spdlog::info("SyncService: Immediate WorkHour sync completed");

            return true;
        }
        catch (const std::exception &e)
        {
            spdlog::error("SyncService: Work hour submission exception - {}", e.what());
            return false;
        }
    }

    // Helper function to safely extract values from JSON
    template <typename T>
    T safeGetValue(const nlohmann::json &item, const std::string &key, const T &defaultValue = T{})
    {
        if (item.contains(key) && !item[key].is_null())
        {
            try
            {
                return item[key].get<T>();
            }
            catch (const std::exception &e)
            {
                spdlog::warn("Failed to parse field '{}': {}", key, e.what());
            }
        }
        return defaultValue;
    }
    ~SyncService()
    {
        stopAutoSync();
    }

private:
    bool checkAndSyncHolidays()
    {
        spdlog::info("SyncService: Checking holiday data completeness");

        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto tm = *std::localtime(&time_t);
        int current_year = tm.tm_year + 1900;

        auto &dao = application_.getHolidayDao();
        auto existing_years = dao.getExistingYears();

        std::vector<int> required_years;
        for (int year = 2024; year <= current_year + 1; ++year)
        {
            if (std::find(existing_years.begin(), existing_years.end(), year) == existing_years.end())
            {
                required_years.push_back(year);
            }
        }

        if (required_years.empty())
        {
            spdlog::info("SyncService: Holiday data is complete for years 2024 to {}", current_year + 1);
            return true;
        }

        spdlog::info("SyncService: Missing holiday data for {} years, starting synchronization", required_years.size());

        bool overall_success = true;
        for (int year : required_years)
        {
            bool year_success = false;
            bool skip_retries = false;

            for (int attempt = 1; attempt <= 3; ++attempt)
            {
                spdlog::info("SyncService: Syncing holiday data for year {} (attempt {}/3)", year, attempt);

                try
                {
                    if (syncHolidays(year))
                    {
                        year_success = true;
                        break;
                    }
                    else
                    {
                        spdlog::warn("SyncService: Holiday sync failed for year {} on attempt {}", year, attempt);
                        if (attempt < 3)
                        {
                            std::this_thread::sleep_for(std::chrono::milliseconds(500));  // Reduced wait time
                        }
                    }
                }
                catch (const std::exception &e)
                {
                    // Other exceptions, continue with retry logic
                    spdlog::warn("SyncService: Holiday sync failed for year {} on attempt {} with error: {}", year, attempt, e.what());
                    if (attempt < 3)
                    {
                        std::this_thread::sleep_for(std::chrono::milliseconds(500));
                    }
                }
            }

            if (!year_success && !skip_retries)
            {
                spdlog::error("SyncService: Failed to sync holiday data for year {} after 3 attempts", year);
                overall_success = false;
            }
            else if (!year_success && skip_retries)
            {
                spdlog::warn("SyncService: Skipped holiday sync for year {} due to empty data", year);
                overall_success = false;
            }
        }

        return overall_success;
    }
    // Unified timer-based synchronization function
    void startUnifiedSyncTimer()
    {
        if (!unified_sync_timer_ || !auto_sync_running_)
        {
            return;
        }

        // Schedule next complete sync in 12 hours
        unified_sync_timer_->expires_after(std::chrono::hours(12));
        unified_sync_timer_->async_wait([this](const asio::error_code &ec) {
            if (!ec && auto_sync_running_)
            {
                spdlog::info("SyncService: Starting scheduled complete synchronization...");

                // Sync all data types
                bool generalSuccess = syncAll();
                spdlog::info("SyncService: Scheduled data synchronization completed with status: {}", generalSuccess ? "SUCCESS" : "FAILED");

                // Sync work hours
                spdlog::info("SyncService: Starting scheduled WorkHour synchronization...");
                performIncrementalWorkHourSync();

                // Schedule next sync
                startUnifiedSyncTimer();
            }
            else if (ec != asio::error::operation_aborted)
            {
                spdlog::error("SyncService: Unified sync timer error: {}", ec.message());
            }
        });
    }

    bool performInitialWorkHourSync()
    {
        try
        {
            // Check if workhour has been synced before
            auto &syncStatusDao = application_.getSyncStatusDao();
            auto lastStatus = syncStatusDao.findByDataType("workhour");

            long long startTime;
            auto now = std::chrono::system_clock::now();
            long long endTime = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();

            if (lastStatus.has_value())
            {
                // Get last successful sync endTime as this sync's startTime
                startTime = getLastSyncTimeFromDatabase("workhour");

                if (startTime == 0)
                {
                    spdlog::warn("SyncService: Could not retrieve last sync time, using 2024-10-01 as fallback");
                    // Fallback to 2024-10-01 if we can't get the last sync time
                    std::tm tm = {};
                    tm.tm_year = 2024 - 1900;
                    tm.tm_mon = 10 - 1;
                    tm.tm_mday = 1;
                    tm.tm_hour = 0;
                    tm.tm_min = 0;
                    tm.tm_sec = 0;
                    auto startTimePoint = std::chrono::system_clock::from_time_t(std::mktime(&tm));
                    startTime = std::chrono::duration_cast<std::chrono::milliseconds>(startTimePoint.time_since_epoch()).count();
                }
                else
                {
                    spdlog::info("SyncService: WorkHour sync record found, performing incremental sync from last sync time");
                }
            }
            else
            {
                // No sync record found, perform initial sync from 2024-10-01 to now
                spdlog::info("SyncService: No WorkHour sync record found, performing initial sync from 2024-10-01");

                // Create timestamp for 2024-10-01 00:00:00
                std::tm tm = {};
                tm.tm_year = 2024 - 1900;  // years since 1900
                tm.tm_mon = 10 - 1;        // months since January (0-11)
                tm.tm_mday = 1;            // day of the month (1-31)
                tm.tm_hour = 0;
                tm.tm_min = 0;
                tm.tm_sec = 0;

                auto startTimePoint = std::chrono::system_clock::from_time_t(std::mktime(&tm));
                startTime = std::chrono::duration_cast<std::chrono::milliseconds>(startTimePoint.time_since_epoch()).count();
            }

            spdlog::info(
                "SyncService: WorkHour sync range - start: {}, end: {} (duration: {} hours)", startTime, endTime, (endTime - startTime) / (1000.0 * 60 * 60));

            // Perform the sync
            bool success = syncWorkHours(startTime, endTime);

            if (success)
            {
                spdlog::info("SyncService: WorkHour sync completed successfully");
            }
            else
            {
                spdlog::error("SyncService: WorkHour sync failed");
            }

            return success;
        }
        catch (const std::exception &e)
        {
            spdlog::error("Error in initial WorkHour sync: {}", e.what());
            return false;
        }
    }

    void performIncrementalWorkHourSync()
    {
        try
        {
            // Get last sync time
            long long lastSyncTime = getLastSyncTime("workhour");
            auto now = std::chrono::system_clock::now();
            long long currentTime = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();

            long long adjustedStartTime;

            if (lastSyncTime == 0)
            {
                // If no previous sync, start from 7 days ago
                auto week_ago = now - std::chrono::hours(24 * 7);
                adjustedStartTime = std::chrono::duration_cast<std::chrono::milliseconds>(week_ago.time_since_epoch()).count();
                spdlog::info("SyncService: No previous sync found, starting from 7 days ago");
            }
            else
            {
                // Add overlap window to catch any backdated entries
                // Use 7-day overlap to ensure we don't miss any historical data insertions
                long long overlapWindow = 7 * 24 * 60 * 60 * 1000;  // 7 days in milliseconds
                adjustedStartTime = lastSyncTime - overlapWindow;

                spdlog::info("SyncService: Incremental sync with 7-day overlap window");
                spdlog::info("SyncService: Original start time: {}, Adjusted start time: {}", lastSyncTime, adjustedStartTime);
                spdlog::info("SyncService: 7-day overlap ensures backdated entries are captured");
            }

            // Sync from adjusted start time to now
            syncWorkHours(adjustedStartTime, currentTime);
        }
        catch (const std::exception &e)
        {
            spdlog::error("Error in incremental WorkHour sync: {}", e.what());
        }
    }

    long long getLastSyncTime(const std::string &dataType)
    {
        return getLastSyncTimeFromDatabase(dataType);
    }

    void updateLastSyncTime(const std::string &dataType, long long timestamp)
    {
        // This function is no longer needed as we store timestamps directly in database
        spdlog::info("Updated last sync time for {}: {}", dataType, timestamp);
    }

    long long getLastSyncTimeFromDatabase(const std::string &dataType)
    {
        auto &dao = application_.getSyncStatusDao();
        auto status = dao.findByDataType(dataType);
        if (status.has_value())
        {
            // Parse sync_time string to timestamp
            std::string syncTimeStr = status.value().sync_time;
            try
            {
                // Convert SQLite datetime string to timestamp
                // Format: "YYYY-MM-DD HH:MM:SS"
                std::tm tm = {};
                std::istringstream ss(syncTimeStr);
                ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");

                if (ss.fail())
                {
                    spdlog::error("Failed to parse sync_time: {}", syncTimeStr);
                    return 0;
                }

                auto timePoint = std::chrono::system_clock::from_time_t(std::mktime(&tm));
                return std::chrono::duration_cast<std::chrono::milliseconds>(timePoint.time_since_epoch()).count();
            }
            catch (const std::exception &e)
            {
                spdlog::error("Error parsing sync_time {}: {}", syncTimeStr, e.what());
                return 0;
            }
        }
        return 0;
    }

    void recordSyncStatus(const std::string &data_type, long long endTime = 0)
    {
        SyncStatus status;
        status.data_type = data_type;
        auto &dao = application_.getSyncStatusDao();
        dao.insertOrUpdate(status);
    }

    std::vector<Product> parseProducts(const nlohmann::json &data)
    {
        std::vector<Product> products;

        if (!data.is_array())
        {
            return products;
        }

        for (const auto &item : data)
        {
            try
            {
                Product product;
                product.id = safeGetValue<int>(item, "id", 0);
                product.product_name = safeGetValue<std::string>(item, "productName");
                product.product_description = safeGetValue<std::string>(item, "productDescription");
                product.product_start_time = safeGetValue<std::string>(item, "productStartTime");
                product.product_end_time = safeGetValue<std::string>(item, "productEndTime");
                product.group_name = safeGetValue<std::string>(item, "groupName");

                products.push_back(product);
            }
            catch (const std::exception &e)
            {
                spdlog::error("Error parsing product: {}", e.what());
            }
        }

        return products;
    }

    std::vector<Project> parseProjects(const nlohmann::json &data)
    {
        std::vector<Project> projects;

        if (!data.is_array())
        {
            return projects;
        }

        for (const auto &item : data)
        {
            try
            {
                Project project;
                project.id = safeGetValue<int>(item, "id", 0);
                project.project_code = safeGetValue<std::string>(item, "projectCode");
                project.project_name = safeGetValue<std::string>(item, "projectName");
                project.short_name = safeGetValue<std::string>(item, "shortName");

                // Handle time fields that might be numbers
                if (item.contains("startTime") && !item["startTime"].is_null())
                {
                    if (item["startTime"].is_number())
                    {
                        project.start_time = std::to_string(item["startTime"].get<double>());
                    }
                    else
                    {
                        project.start_time = safeGetValue<std::string>(item, "startTime");
                    }
                }
                if (item.contains("endTime") && !item["endTime"].is_null())
                {
                    if (item["endTime"].is_number())
                    {
                        project.end_time = std::to_string(item["endTime"].get<double>());
                    }
                    else
                    {
                        project.end_time = safeGetValue<std::string>(item, "endTime");
                    }
                }
                if (item.contains("planStartTime") && !item["planStartTime"].is_null())
                {
                    if (item["planStartTime"].is_number())
                    {
                        project.plan_start_time = std::to_string(item["planStartTime"].get<double>());
                    }
                    else
                    {
                        project.plan_start_time = safeGetValue<std::string>(item, "planStartTime");
                    }
                }
                if (item.contains("planEndTime") && !item["planEndTime"].is_null())
                {
                    if (item["planEndTime"].is_number())
                    {
                        project.plan_end_time = std::to_string(item["planEndTime"].get<double>());
                    }
                    else
                    {
                        project.plan_end_time = safeGetValue<std::string>(item, "planEndTime");
                    }
                }

                project.duty_persons = safeGetValue<std::string>(item, "dutyPersons");
                project.duty_departments = safeGetValue<std::string>(item, "dutyDepartments");
                project.join_departments = safeGetValue<std::string>(item, "joinDepartments");
                project.join_persons = safeGetValue<std::string>(item, "joinPersons");
                project.project_type = safeGetValue<std::string>(item, "projectType");
                project.project_state = safeGetValue<std::string>(item, "projectState");

                // Handle relProductList
                if (item.contains("relProductList") && !item["relProductList"].is_null())
                {
                    try
                    {
                        project.rel_product_list = item["relProductList"].dump();
                    }
                    catch (const std::exception &e)
                    {
                        spdlog::warn("Failed to serialize relProductList: {}", e.what());
                        project.rel_product_list = "[]";
                    }
                }

                project.rel_customer_info = safeGetValue<int>(item, "relCustomerInfo", 0);
                project.project_comment = safeGetValue<std::string>(item, "projectComment");
                project.contract_name = safeGetValue<std::string>(item, "contractName");

                projects.push_back(project);
            }
            catch (const std::exception &e)
            {
                spdlog::error("Error parsing project: {}", e.what());
            }
        }

        return projects;
    }

    std::vector<Customer> parseCustomers(const nlohmann::json &data)
    {
        std::vector<Customer> customers;

        if (!data.is_array())
        {
            return customers;
        }

        for (const auto &item : data)
        {
            try
            {
                Customer customer;
                customer.id = safeGetValue<int>(item, "id", 0);
                customer.unit_name = safeGetValue<std::string>(item, "unitName");
                customer.contact = safeGetValue<std::string>(item, "contact");
                customer.department = safeGetValue<std::string>(item, "department");
                customer.phones = safeGetValue<std::string>(item, "phones");
                customer.group_name = safeGetValue<std::string>(item, "groupName");

                customers.push_back(customer);
            }
            catch (const std::exception &e)
            {
                spdlog::error("Error parsing customer: {}", e.what());
            }
        }

        return customers;
    }

    std::vector<User> parseUsers(const nlohmann::json &data)
    {
        std::vector<User> users;

        try
        {
            // data is already the inner data object from the API response
            // We need to parse it as UserData, not UserApiResponse
            if (data.contains("list") && data["list"].is_array())
            {
                for (const auto &userJson : data["list"])
                {
                    User user;
                    user.id = safeGetValue<std::string>(userJson, "id");
                    user.account = safeGetValue<std::string>(userJson, "account");
                    user.name = safeGetValue<std::string>(userJson, "name");
                    user.gender = safeGetValue<std::string>(userJson, "gender");
                    user.mobile = safeGetValue<std::string>(userJson, "mobile");
                    user.email = safeGetValue<std::string>(userJson, "email");
                    user.phone = safeGetValue<std::string>(userJson, "phone");
                    user.post = safeGetValue<std::string>(userJson, "post");
                    user.type = safeGetValue<std::string>(userJson, "type");
                    user.enable = safeGetValue<bool>(userJson, "enable", true);
                    user.dept_name = safeGetValue<std::string>(userJson, "deptName");
                    user.team_name = safeGetValue<std::string>(userJson, "teamName");
                    user.role_name = safeGetValue<std::string>(userJson, "roleName");

                    users.push_back(user);
                }
                spdlog::info("SyncService: Successfully parsed {} users", users.size());
            }
            else
            {
                spdlog::warn("SyncService: No user list found in response data");
            }
        }
        catch (const std::exception &e)
        {
            spdlog::error("Error parsing users response: {}", e.what());
        }

        return users;
    }

    std::vector<WorkHour> parseWorkHours(const nlohmann::json &data)
    {
        std::vector<WorkHour> workHours;

        try
        {
            // data is already the inner data object from the API response
            // We need to parse it as WorkHourData, not WorkHourApiResponse
            if (data.contains("list") && data["list"].is_array())
            {
                for (const auto &remoteWorkHourJson : data["list"])
                {
                    // Parse each work hour entry
                    long long employee_id = safeGetValue<long long>(remoteWorkHourJson, "employeeId", 0);
                    std::string employee_name = safeGetValue<std::string>(remoteWorkHourJson, "employeeName");
                    long long submit_date = safeGetValue<long long>(remoteWorkHourJson, "submitDate", 0);
                    long long create_time = safeGetValue<long long>(remoteWorkHourJson, "createTime", 0);

                    // Parse time consumed list
                    if (remoteWorkHourJson.contains("timeConsumedList") && remoteWorkHourJson["timeConsumedList"].is_array())
                    {
                        for (const auto &timeConsumedJson : remoteWorkHourJson["timeConsumedList"])
                        {
                            WorkHour workHour;

                            // Use the timeConsumedList item's id as unique identifier
                            workHour.id = safeGetValue<int>(timeConsumedJson, "id", 0);
                            workHour.user_id = std::to_string(employee_id);
                            workHour.user_name = employee_name;

                            // Handle projectId which can be null
                            if (timeConsumedJson.contains("projectId") && !timeConsumedJson["projectId"].is_null())
                            {
                                workHour.project_id = safeGetValue<int>(timeConsumedJson, "projectId", 0);
                            }
                            else
                            {
                                workHour.project_id = 0;  // Default to 0 for null projectId
                            }

                            workHour.duration_hours = safeGetValue<double>(timeConsumedJson, "submitWorkHour", 0.0) / 60.0;  // Convert minutes to hours
                            workHour.work_description = safeGetValue<std::string>(timeConsumedJson, "submitComment");

                            // Determine work type based on type field
                            int work_type = safeGetValue<int>(timeConsumedJson, "type", 2);
                            switch (work_type)
                            {
                            case 0:
                                workHour.work_type = "Management";
                                break;
                            case 1:
                                workHour.work_type = "Meeting";
                                break;
                            case 2:
                                workHour.work_type = "Development";
                                break;
                            case 3:
                                workHour.work_type = "Testing";
                                break;
                            case 4:
                                workHour.work_type = "Operations";
                                break;
                            default:
                                workHour.work_type = "Development";
                                break;
                            }

                            // Convert timestamp to date string
                            if (submit_date > 0)
                            {
                                auto tmt = static_cast<time_t>(submit_date / 1000);
                                std::tm *tm = std::gmtime(&tmt);
                                char date_str[11];
                                std::strftime(date_str, sizeof(date_str), "%Y-%m-%d", tm);
                                workHour.work_date = date_str;
                            }

                            // Use the timeConsumedList item's createTime and updateTime
                            long long item_create_time = safeGetValue<long long>(timeConsumedJson, "createTime", create_time);
                            long long item_update_time = safeGetValue<long long>(timeConsumedJson, "updateTime", create_time);

                            if (item_create_time > 0)
                            {
                                auto created_time_t = static_cast<time_t>(item_create_time / 1000);
                                std::tm *created_tm = std::gmtime(&created_time_t);
                                char created_str[20];
                                std::strftime(created_str, sizeof(created_str), "%Y-%m-%d %H:%M:%S", created_tm);
                                workHour.created_time = created_str;
                            }

                            if (item_update_time > 0)
                            {
                                auto updated_time_t = static_cast<time_t>(item_update_time / 1000);
                                std::tm *updated_tm = std::gmtime(&updated_time_t);
                                char updated_str[20];
                                std::strftime(updated_str, sizeof(updated_str), "%Y-%m-%d %H:%M:%S", updated_tm);
                                workHour.updated_time = updated_str;
                            }

                            spdlog::debug("SyncService: Parsed work hour - ID: {}, User: {}, Project: {}, Hours: {}, Description: {}", workHour.id,
                                workHour.user_name, workHour.project_id, workHour.duration_hours, workHour.work_description);

                            workHours.push_back(workHour);
                        }
                    }
                }
                spdlog::info("SyncService: Successfully parsed {} work hours", workHours.size());
            }
            else
            {
                spdlog::warn("SyncService: No work hour list found in response data");
            }
        }
        catch (const std::exception &e)
        {
            spdlog::error("Error parsing work hours response: {}", e.what());
        }

        return workHours;
    }

    std::vector<Holiday> parseHolidays(const nlohmann::json &data)
    {
        std::vector<Holiday> holidays;

        try
        {
            if (!data.contains("year") || !data.contains("days"))
            {
                spdlog::error("SyncService: Invalid holiday data format - missing year or days");
                return holidays;
            }

            int year = safeGetValue<int>(data, "year", 0);
            if (year == 0)
            {
                spdlog::error("SyncService: Invalid year in holiday data");
                return holidays;
            }

            if (data["days"].is_array())
            {
                for (const auto &dayJson : data["days"])
                {
                    Holiday holiday;
                    holiday.year = year;
                    holiday.name = safeGetValue<std::string>(dayJson, "name");
                    holiday.date = safeGetValue<std::string>(dayJson, "date");
                    holiday.is_off_day = safeGetValue<bool>(dayJson, "isOffDay", false);

                    if (!holiday.name.empty() && !holiday.date.empty())
                    {
                        holidays.push_back(holiday);
                    }
                }
                spdlog::info("SyncService: Successfully parsed {} holidays for year {}", holidays.size(), year);
            }
            else
            {
                spdlog::warn("SyncService: No holiday days array found in response data");
            }
        }
        catch (const std::exception &e)
        {
            spdlog::error("Error parsing holidays response: {}", e.what());
        }

        return holidays;
    }

private:
    // Reference to Application instance for accessing other components
    Application &application_;
    std::atomic<bool> auto_sync_running_{ false };
    std::thread auto_sync_thread_;

    // ASIO components for timer-based synchronization
    std::unique_ptr<asio::io_context> io_context_;
    std::unique_ptr<asio::steady_timer> unified_sync_timer_;
    std::unique_ptr<std::thread> io_thread_;

    BackendConfig config_;
    HttpClient http_client_;
};
