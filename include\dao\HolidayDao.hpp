#pragma once

#include "models/Holiday.hpp"
#include "database/DatabaseManager.hpp"
#include <vector>
#include <optional>
#include <spdlog/spdlog.h>

class HolidayDao {
public:
    HolidayDao(DatabaseManager& db_manager) : db_manager_(db_manager) {}
    
    bool insert(const Holiday& holiday) {
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, R"(
                INSERT OR REPLACE INTO holidays (year, name, date, is_off_day)
                VALUES (?, ?, ?, ?)
            )");
            
            query.bind(1, holiday.year);
            query.bind(2, holiday.name);
            query.bind(3, holiday.date);
            query.bind(4, holiday.is_off_day ? 1 : 0);
            
            return query.exec() > 0;
        } catch (const std::exception& e) {
            spdlog::error("HolidayDao::insert error: {}", e.what());
            return false;
        }
    }
    
    bool update(const Holiday& holiday) {
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, R"(
                UPDATE holidays
                SET name=?, is_off_day=?
                WHERE year=? AND date=?
            )");
            
            query.bind(1, holiday.name);
            query.bind(2, holiday.is_off_day ? 1 : 0);
            query.bind(3, holiday.year);
            query.bind(4, holiday.date);
            
            return query.exec() > 0;
        } catch (const std::exception& e) {
            spdlog::error("HolidayDao::update error: {}", e.what());
            return false;
        }
    }
    
    bool deleteByYear(int year) {
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, "DELETE FROM holidays WHERE year=?");
            query.bind(1, year);
            return query.exec() >= 0;
        } catch (const std::exception& e) {
            spdlog::error("HolidayDao::deleteByYear error: {}", e.what());
            return false;
        }
    }
    
    std::vector<Holiday> findByYear(int year) {
        std::vector<Holiday> holidays;
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, R"(
                SELECT id, year, name, date, is_off_day
                FROM holidays
                WHERE year=?
                ORDER BY date
            )");
            query.bind(1, year);
            
            while (query.executeStep()) {
                Holiday holiday;
                holiday.id = query.getColumn(0).getInt();
                holiday.year = query.getColumn(1).getInt();
                holiday.name = query.getColumn(2).getString();
                holiday.date = query.getColumn(3).getString();
                holiday.is_off_day = query.getColumn(4).getInt() == 1;
                holidays.push_back(holiday);
            }
        } catch (const std::exception& e) {
            spdlog::error("HolidayDao::findByYear error: {}", e.what());
        }
        return holidays;
    }
    
    std::vector<Holiday> findAll() {
        std::vector<Holiday> holidays;
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, R"(
                SELECT id, year, name, date, is_off_day
                FROM holidays
                ORDER BY year, date
            )");
            
            while (query.executeStep()) {
                Holiday holiday;
                holiday.id = query.getColumn(0).getInt();
                holiday.year = query.getColumn(1).getInt();
                holiday.name = query.getColumn(2).getString();
                holiday.date = query.getColumn(3).getString();
                holiday.is_off_day = query.getColumn(4).getInt() == 1;
                holidays.push_back(holiday);
            }
        } catch (const std::exception& e) {
            spdlog::error("HolidayDao::findAll error: {}", e.what());
        }
        return holidays;
    }
    
    std::optional<Holiday> findByDate(const std::string& date) {
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, R"(
                SELECT id, year, name, date, is_off_day
                FROM holidays
                WHERE date=?
            )");
            query.bind(1, date);
            
            if (query.executeStep()) {
                Holiday holiday;
                holiday.id = query.getColumn(0).getInt();
                holiday.year = query.getColumn(1).getInt();
                holiday.name = query.getColumn(2).getString();
                holiday.date = query.getColumn(3).getString();
                holiday.is_off_day = query.getColumn(4).getInt() == 1;
                return holiday;
            }
            return std::nullopt;
        } catch (const std::exception& e) {
            spdlog::error("HolidayDao::findByDate error: {}", e.what());
            return std::nullopt;
        }
    }
    
    bool insertOrUpdate(const Holiday& holiday) {
        auto existing = findByDate(holiday.date);
        if (existing.has_value()) {
            return update(holiday);
        } else {
            return insert(holiday);
        }
    }

    // Batch insert for better performance during sync
    bool batchInsert(const std::vector<Holiday>& holidays) {
        if (holidays.empty()) {
            return true;
        }

        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Transaction transaction(db);

            // Prepare statement once for all inserts
            SQLite::Statement query(db, R"(
                INSERT OR REPLACE INTO holidays (year, name, date, is_off_day)
                VALUES (?, ?, ?, ?)
            )");

            int processed = 0;
            for (const auto& holiday : holidays) {
                query.bind(1, holiday.year);
                query.bind(2, holiday.name);
                query.bind(3, holiday.date);
                query.bind(4, holiday.is_off_day ? 1 : 0);

                if (query.exec() > 0) {
                    processed++;
                }
                query.reset();
            }

            transaction.commit();
            spdlog::info("HolidayDao: Batch insert completed - {}/{} records processed successfully",
                        processed, holidays.size());
            return processed == holidays.size();

        } catch (const std::exception& e) {
            spdlog::error("HolidayDao::batchInsert error: {}", e.what());
            return false;
        }
    }
    
    int getCountByYear(int year) {
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, "SELECT COUNT(*) FROM holidays WHERE year=?");
            query.bind(1, year);
            if (query.executeStep()) {
                return query.getColumn(0).getInt();
            }
        } catch (const std::exception& e) {
            spdlog::error("HolidayDao::getCountByYear error: {}", e.what());
        }
        return 0;
    }
    
    std::vector<int> getExistingYears() {
        std::vector<int> years;
        try {
            auto& db = db_manager_.getDatabase();
            SQLite::Statement query(db, "SELECT DISTINCT year FROM holidays ORDER BY year");
            
            while (query.executeStep()) {
                years.push_back(query.getColumn(0).getInt());
            }
        } catch (const std::exception& e) {
            spdlog::error("HolidayDao::getExistingYears error: {}", e.what());
        }
        return years;
    }

private:
    DatabaseManager& db_manager_;
};
