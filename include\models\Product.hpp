#pragma once

#include <string>
#include <nlohmann/json.hpp>

struct Product {
    int id = 0;
    std::string product_name;
    std::string product_description;
    std::string product_start_time;
    std::string product_end_time;
    std::string group_name;

    NLOHMANN_DEFINE_TYPE_INTRUSIVE(Product, id, product_name, product_description,
                                   product_start_time, product_end_time, group_name)
};
